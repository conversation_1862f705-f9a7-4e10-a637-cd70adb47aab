#!/usr/bin/env python3
"""
Test script for Salary Slip with Employee Advance Integration

This script tests the basic functionality of the integration.
Run this from your Frappe bench directory:

bench execute test_salary_slip_advance_integration.test_integration
"""

import frappe
from frappe import _


def test_integration():
    """Test the salary slip advance integration"""
    
    print("Testing Salary Slip with Employee Advance Integration...")
    print("=" * 60)
    
    # Test 1: Check if print format exists
    test_print_format_exists()
    
    # Test 2: Check if utility functions are accessible
    test_utility_functions()
    
    # Test 3: Check if salary slip methods are available
    test_salary_slip_methods()
    
    # Test 4: Check if hooks are registered
    test_hooks_registration()
    
    print("\n" + "=" * 60)
    print("Integration test completed!")


def test_print_format_exists():
    """Test if the print format exists"""
    print("\n1. Testing Print Format...")
    
    try:
        print_format = frappe.get_doc("Print Format", "Salary Slip with Advance")
        if print_format:
            print("   ✓ Print Format 'Salary Slip with Advance' exists")
            print(f"   ✓ Doc Type: {print_format.doc_type}")
            print(f"   ✓ Module: {print_format.module}")
            print(f"   ✓ Disabled: {print_format.disabled}")
        else:
            print("   ✗ Print Format not found")
    except frappe.DoesNotExistError:
        print("   ✗ Print Format 'Salary Slip with Advance' does not exist")
        print("   → Run the installation script to create it")
    except Exception as e:
        print(f"   ✗ Error checking print format: {str(e)}")


def test_utility_functions():
    """Test if utility functions are accessible"""
    print("\n2. Testing Utility Functions...")
    
    try:
        from hrms.payroll.doctype.salary_slip.salary_slip_advance_utils import (
            get_employee_advance_summary,
            get_advance_deductions_in_salary_slip,
            create_advance_deduction_additional_salary,
            get_employee_advance_balance,
            validate_advance_deduction_in_salary_slip
        )
        
        print("   ✓ get_employee_advance_summary imported successfully")
        print("   ✓ get_advance_deductions_in_salary_slip imported successfully")
        print("   ✓ create_advance_deduction_additional_salary imported successfully")
        print("   ✓ get_employee_advance_balance imported successfully")
        print("   ✓ validate_advance_deduction_in_salary_slip imported successfully")
        
        # Test a simple function call (if employees exist)
        employees = frappe.get_all("Employee", limit=1)
        if employees:
            employee = employees[0].name
            result = get_employee_advance_balance(employee)
            print(f"   ✓ get_employee_advance_balance test successful for {employee}")
        else:
            print("   ⚠ No employees found to test with")
            
    except ImportError as e:
        print(f"   ✗ Import error: {str(e)}")
    except Exception as e:
        print(f"   ✗ Error testing utility functions: {str(e)}")


def test_salary_slip_methods():
    """Test if salary slip methods are available"""
    print("\n3. Testing Salary Slip Methods...")
    
    try:
        from hrms.payroll.doctype.salary_slip.salary_slip import SalarySlip
        
        # Check if our new methods exist
        if hasattr(SalarySlip, 'get_employee_advance_details'):
            print("   ✓ get_employee_advance_details method exists")
        else:
            print("   ✗ get_employee_advance_details method not found")
            
        if hasattr(SalarySlip, 'get_advance_deductions_in_salary'):
            print("   ✓ get_advance_deductions_in_salary method exists")
        else:
            print("   ✗ get_advance_deductions_in_salary method not found")
            
    except ImportError as e:
        print(f"   ✗ Import error: {str(e)}")
    except Exception as e:
        print(f"   ✗ Error testing salary slip methods: {str(e)}")


def test_hooks_registration():
    """Test if hooks are properly registered"""
    print("\n4. Testing Hooks Registration...")
    
    try:
        from hrms import hooks
        
        if hasattr(hooks, 'doc_events'):
            doc_events = hooks.doc_events
            if 'Salary Slip' in doc_events:
                salary_slip_hooks = doc_events['Salary Slip']
                if 'validate' in salary_slip_hooks:
                    validate_hook = salary_slip_hooks['validate']
                    if 'salary_slip_advance_utils.validate_advance_deduction_in_salary_slip' in validate_hook:
                        print("   ✓ Salary Slip validation hook registered")
                    else:
                        print("   ✗ Salary Slip validation hook not found in validate")
                else:
                    print("   ✗ No validate hook found for Salary Slip")
            else:
                print("   ✗ No hooks found for Salary Slip")
        else:
            print("   ✗ No doc_events found in hooks")
            
    except Exception as e:
        print(f"   ✗ Error testing hooks: {str(e)}")


def test_with_sample_data():
    """Test with sample data (optional)"""
    print("\n5. Testing with Sample Data...")
    
    try:
        # Check if we have any employees with advances
        employees_with_advances = frappe.db.sql("""
            SELECT DISTINCT ea.employee, e.employee_name
            FROM `tabEmployee Advance` ea
            JOIN `tabEmployee` e ON ea.employee = e.name
            WHERE ea.docstatus = 1
            LIMIT 5
        """, as_dict=True)
        
        if employees_with_advances:
            print(f"   ✓ Found {len(employees_with_advances)} employees with advances")
            
            for emp in employees_with_advances:
                from hrms.payroll.doctype.salary_slip.salary_slip_advance_utils import get_employee_advance_summary
                summary = get_employee_advance_summary(emp.employee)
                print(f"   ✓ {emp.employee_name}: {summary['summary']['count']} advances")
        else:
            print("   ⚠ No employees with advances found")
            
    except Exception as e:
        print(f"   ✗ Error testing with sample data: {str(e)}")


def install_print_format():
    """Install the print format if it doesn't exist"""
    print("\nInstalling Print Format...")
    
    try:
        from hrms.setup.install_salary_slip_advance_print_format import install_salary_slip_advance_print_format
        install_salary_slip_advance_print_format()
        print("   ✓ Print format installation completed")
    except Exception as e:
        print(f"   ✗ Error installing print format: {str(e)}")


if __name__ == "__main__":
    # If running directly, also try to install the print format
    install_print_format()
    test_integration()
