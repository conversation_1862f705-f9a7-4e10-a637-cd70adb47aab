# Salary Slip with Employee Advance Integration

This implementation provides a comprehensive solution for integrating Employee Advance functionality with Salary Slip, including a custom print format that displays advance information.

## Features

### 1. Custom Print Format
- **Name**: "Salary Slip with Advance"
- **Location**: `apps/hrms/hrms/payroll/print_format/salary_slip_with_advance/`
- **Features**:
  - Displays recent employee advances in the salary slip
  - Shows advance deductions with reference to the original advance
  - Professional layout with employee and company information
  - Includes advance summary table

### 2. Enhanced Salary Slip Functionality
- **New Methods Added**:
  - `get_employee_advance_details()`: Get advance details for an employee
  - `get_advance_deductions_in_salary()`: Get advance deductions in current salary slip

### 3. Utility Functions
- **File**: `apps/hrms/hrms/payroll/doctype/salary_slip/salary_slip_advance_utils.py`
- **Functions**:
  - `get_employee_advance_summary()`: Get comprehensive advance summary
  - `get_advance_deductions_in_salary_slip()`: Get advance deductions for a salary slip
  - `create_advance_deduction_additional_salary()`: Create advance deduction
  - `get_employee_advance_balance()`: Get total advance balance
  - `validate_advance_deduction_in_salary_slip()`: Validation hook

### 4. Enhanced UI Features
- **Employee Advance Button**: Create advance deductions directly from salary slip
- **View Advance Details Button**: View comprehensive advance information
- **Interactive Dialogs**: User-friendly interface for advance management

## Installation

### Step 1: Install the Print Format
```bash
# Navigate to your Frappe bench directory
cd /path/to/your/frappe-bench

# Run the installation script
bench execute hrms.setup.install_salary_slip_advance_print_format.install_salary_slip_advance_print_format
```

### Step 2: Restart the System
```bash
# Restart the bench to load new changes
bench restart
```

### Step 3: Clear Cache
```bash
# Clear cache to ensure all changes are loaded
bench clear-cache
```

## Usage

### 1. Using the Print Format

1. **Navigate to Salary Slip**:
   - Go to any Salary Slip document
   - Click on the "Print" button
   - Select "Salary Slip with Advance" from the print format dropdown

2. **What You'll See**:
   - Standard salary slip information
   - Recent Employee Advances table (if any exist)
   - Advance deductions clearly marked in the deductions section
   - Professional layout suitable for official documentation

### 2. Creating Advance Deductions

1. **From Salary Slip Form**:
   - Open a draft Salary Slip
   - Click "Tools" → "Employee Advance"
   - Select the advance to deduct from
   - Choose the salary component for deduction
   - Enter the deduction amount
   - Click "Create Deduction"

2. **The System Will**:
   - Create an Additional Salary record
   - Link it to the Employee Advance
   - Update the advance return amount
   - Refresh the salary slip with the new deduction

### 3. Viewing Advance Details

1. **From Salary Slip Form**:
   - Click "View" → "View Advance Details"
   - See comprehensive advance summary
   - View recent advances with status
   - Click on advance IDs to open advance documents

## Technical Implementation

### File Structure
```
apps/hrms/hrms/payroll/print_format/salary_slip_with_advance/
├── __init__.py
├── salary_slip_with_advance.json
└── salary_slip_with_advance.html

apps/hrms/hrms/payroll/doctype/salary_slip/
├── salary_slip.py (enhanced)
├── salary_slip.js (enhanced)
└── salary_slip_advance_utils.py (new)

apps/hrms/hrms/setup/
└── install_salary_slip_advance_print_format.py (new)
```

### Key Code Components

#### 1. Print Format HTML Template
The HTML template uses Jinja2 templating to:
- Query employee advances dynamically
- Display advance information in a structured table
- Show advance deductions with references
- Maintain professional formatting

#### 2. JavaScript Enhancements
- Added custom buttons to the Salary Slip form
- Created interactive dialogs for advance management
- Implemented AJAX calls for real-time data fetching

#### 3. Python Utilities
- Comprehensive utility functions for advance management
- Validation hooks to ensure data integrity
- API endpoints for frontend interactions

### Database Relationships

```
Employee Advance → Additional Salary → Salary Detail (Deduction)
     ↓                    ↓                    ↓
   Salary Slip ←────────────────────────────────┘
```

## Customization

### Modifying the Print Format

1. **Edit HTML Template**:
   ```bash
   # Edit the HTML file
   apps/hrms/hrms/payroll/print_format/salary_slip_with_advance/salary_slip_with_advance.html
   ```

2. **Update Styling**:
   - Modify CSS classes in the HTML template
   - Add custom styles for better presentation

3. **Change Data Display**:
   - Modify the Jinja2 queries to show different advance information
   - Add or remove columns from the advance table

### Adding New Features

1. **Extend Utility Functions**:
   ```python
   # Add new functions to salary_slip_advance_utils.py
   def your_custom_function():
       # Your implementation
       pass
   ```

2. **Enhance JavaScript**:
   ```javascript
   // Add new functionality to salary_slip.js
   frappe.ui.form.on("Salary Slip", {
       your_custom_event: function(frm) {
           // Your implementation
       }
   });
   ```

## Troubleshooting

### Common Issues

1. **Print Format Not Appearing**:
   - Ensure the installation script ran successfully
   - Clear cache: `bench clear-cache`
   - Restart: `bench restart`

2. **Advance Data Not Showing**:
   - Check if employee has submitted advances
   - Verify advance status (should be "Paid" or "Unpaid")
   - Ensure advances have remaining balance

3. **JavaScript Errors**:
   - Check browser console for errors
   - Ensure all required files are loaded
   - Verify custom button permissions

### Debug Mode

Enable debug mode to see detailed error messages:
```bash
# In site_config.json
{
    "developer_mode": 1,
    "debug": 1
}
```

## API Reference

### Utility Functions

#### `get_employee_advance_summary(employee, start_date=None, end_date=None)`
Returns comprehensive advance summary for an employee.

#### `create_advance_deduction_additional_salary(employee_advance, salary_component, amount, payroll_date)`
Creates Additional Salary for advance deduction.

#### `get_employee_advance_balance(employee)`
Returns total advance balance for an employee.

### JavaScript Events

#### `show_employee_advance_dialog(frm)`
Shows dialog to create advance deductions.

#### `show_advance_details(frm)`
Displays comprehensive advance information.

## Support

For issues or questions:
1. Check the troubleshooting section above
2. Review the code implementation
3. Test in a development environment first
4. Ensure all dependencies are properly installed

## License

This implementation follows the same license as the HRMS app (GNU General Public License v3).
