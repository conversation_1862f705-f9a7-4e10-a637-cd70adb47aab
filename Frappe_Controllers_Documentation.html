<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Frappe Framework Controllers and Controller Hooks Documentation</title>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            line-height: 1.6;
            margin: 0;
            padding: 20px;
            background-color: #f8f9fa;
            color: #333;
        }
        .container {
            max-width: 1000px;
            margin: 0 auto;
            background: white;
            padding: 40px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        h1 {
            color: #2c3e50;
            border-bottom: 3px solid #3498db;
            padding-bottom: 10px;
            margin-bottom: 30px;
        }
        h2 {
            color: #34495e;
            border-bottom: 2px solid #ecf0f1;
            padding-bottom: 8px;
            margin-top: 40px;
            margin-bottom: 20px;
        }
        h3 {
            color: #2980b9;
            margin-top: 30px;
            margin-bottom: 15px;
        }
        h4 {
            color: #8e44ad;
            margin-top: 25px;
            margin-bottom: 10px;
        }
        .meta-info {
            background: #ecf0f1;
            padding: 15px;
            border-radius: 5px;
            margin-bottom: 30px;
        }
        .meta-info p {
            margin: 5px 0;
            font-weight: bold;
        }
        code {
            background: #f8f9fa;
            padding: 2px 6px;
            border-radius: 3px;
            font-family: 'Courier New', monospace;
            color: #e74c3c;
        }
        pre {
            background: #2c3e50;
            color: #ecf0f1;
            padding: 20px;
            border-radius: 5px;
            overflow-x: auto;
            margin: 15px 0;
            font-family: 'Courier New', monospace;
            line-height: 1.4;
        }
        pre code {
            background: none;
            color: inherit;
            padding: 0;
        }
        .hook-info {
            background: #e8f5e8;
            border-left: 4px solid #27ae60;
            padding: 15px;
            margin: 15px 0;
            border-radius: 0 5px 5px 0;
        }
        .hook-info strong {
            color: #27ae60;
        }
        .toc {
            background: #f8f9fa;
            border: 1px solid #dee2e6;
            border-radius: 5px;
            padding: 20px;
            margin-bottom: 30px;
        }
        .toc h2 {
            margin-top: 0;
            border-bottom: none;
            color: #495057;
        }
        .toc ul {
            list-style-type: none;
            padding-left: 0;
        }
        .toc li {
            margin: 8px 0;
        }
        .toc a {
            color: #007bff;
            text-decoration: none;
            padding: 5px 0;
            display: block;
        }
        .toc a:hover {
            color: #0056b3;
            text-decoration: underline;
        }
        .warning {
            background: #fff3cd;
            border: 1px solid #ffeaa7;
            border-radius: 5px;
            padding: 15px;
            margin: 15px 0;
            border-left: 4px solid #f39c12;
        }
        .tip {
            background: #d1ecf1;
            border: 1px solid #bee5eb;
            border-radius: 5px;
            padding: 15px;
            margin: 15px 0;
            border-left: 4px solid #17a2b8;
        }
        .page-break {
            page-break-before: always;
        }
        @media print {
            body {
                background: white;
                font-size: 12px;
            }
            .container {
                box-shadow: none;
                padding: 20px;
            }
            pre {
                font-size: 10px;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>Frappe Framework Controllers and Controller Hooks Documentation</h1>
        
        <div class="meta-info">
            <p><strong>Version:</strong> 1.0</p>
            <p><strong>Date:</strong> June 30, 2025</p>
            <p><strong>Author:</strong> Codebase Analysis</p>
            <p><strong>Framework:</strong> Frappe Framework v15+</p>
        </div>

        <div class="toc">
            <h2>Table of Contents</h2>
            <ul>
                <li><a href="#controller-overview">1. Controller Overview</a></li>
                <li><a href="#document-lifecycle-hooks">2. Document Lifecycle Hooks</a></li>
                <li><a href="#validation-hooks">3. Validation Hooks</a></li>
                <li><a href="#submission-hooks">4. Submission Hooks</a></li>
                <li><a href="#update-hooks">5. Update Hooks</a></li>
                <li><a href="#deletion-hooks">6. Deletion Hooks</a></li>
                <li><a href="#custom-hooks">7. Custom Hooks</a></li>
                <li><a href="#hook-registration">8. Hook Registration</a></li>
                <li><a href="#best-practices">9. Best Practices</a></li>
                <li><a href="#execution-order">10. Hook Execution Order</a></li>
            </ul>
        </div>

        <section id="controller-overview">
            <h2>1. Controller Overview</h2>
            <p>Controllers in Frappe are Python classes that inherit from <code>Document</code> and contain business logic for doctypes. They provide hooks that are automatically called during the document lifecycle.</p>
            
            <pre><code>from frappe.model.document import Document

class MyDoctype(Document):
    def validate(self):
        # Custom validation logic
        pass</code></pre>

            <div class="tip">
                <strong>Key Points:</strong>
                <ul>
                    <li>Controllers handle business logic for doctypes</li>
                    <li>Hooks are automatically triggered during document operations</li>
                    <li>Multiple hooks can be registered for the same event</li>
                    <li>Hooks can be defined in controller files or registered globally in hooks.py</li>
                </ul>
            </div>
        </section>

        <section id="document-lifecycle-hooks" class="page-break">
            <h2>2. Document Lifecycle Hooks</h2>

            <h3>2.1 before_insert()</h3>
            <div class="hook-info">
                <strong>When:</strong> Called before a new document is inserted into the database<br>
                <strong>Use Case:</strong> Set default values, generate auto-fields, prepare data
            </div>

            <h4>Example from Container Reception:</h4>
            <pre><code>def before_insert(self):
    self.posting_datetime = now_datetime()</code></pre>

            <h4>Example from Therapy Plan:</h4>
            <pre><code>def before_insert(doc, method):
    total_sessions = 0
    total_sessions_cancelled = 0
    for entry in doc.therapy_plan_details:
        if entry.no_of_sessions:
            total_sessions += entry.no_of_sessions
        if entry.sessions_cancelled:
            total_sessions_cancelled += entry.sessions_cancelled
    
    doc.total_sessions = total_sessions
    doc.total_sessions_cancelled = total_sessions_cancelled</code></pre>

            <h3>2.2 after_insert()</h3>
            <div class="hook-info">
                <strong>When:</strong> Called after a new document is successfully inserted<br>
                <strong>Use Case:</strong> Create related documents, update linked records, send notifications
            </div>

            <h4>Example from Container:</h4>
            <pre><code>def after_insert(self):
    if self.container_no and self.container_reception:
        self.update_m_bl_based_container_details()
        self.update_hbl_based_container_details()
        self.validate_place_of_destination()
        self.posting_date = getdate(self.creation)
        self.db_update()
        self.db_update_all()</code></pre>

            <h3>2.3 before_save()</h3>
            <div class="hook-info">
                <strong>When:</strong> Called before every save operation (insert and update)<br>
                <strong>Use Case:</strong> Data transformation, calculations, setting computed fields
            </div>

            <h4>Example from Container Reception:</h4>
            <pre><code>def before_save(self):
    if not self.company:
        self.company = frappe.defaults.get_user_default("Company")
    
    if not self.posting_date:
        self.posting_date = nowdate()</code></pre>
        </section>

        <section id="validation-hooks" class="page-break">
            <h2>3. Validation Hooks</h2>

            <h3>3.1 validate()</h3>
            <div class="hook-info">
                <strong>When:</strong> Called during validation phase before save<br>
                <strong>Use Case:</strong> Business rule validation, data integrity checks
            </div>

            <h4>Example from Employee Advance:</h4>
            <pre><code>def validate(self):
    validate_active_employee(self.employee)
    self.validate_exchange_rate()
    self.set_status()
    self.set_pending_amount()</code></pre>

            <h4>Example from Company Budget Control:</h4>
            <pre><code>def validate(self):
    self.validate_required_fields()
    self.validate_fixed_target_logic()
    self.validate_unique_company()

def validate_required_fields(self):
    if self.target_calculation_method in ["Fixed Target", "Revenue-Based Target"] and not self.frequency:
        frappe.throw("Please select a <b>Frequency</b> for revenue calculations.")</code></pre>

            <h3>3.2 before_validate()</h3>
            <div class="hook-info">
                <strong>When:</strong> Called before the validate() method<br>
                <strong>Use Case:</strong> Prepare data for validation
            </div>

            <h4>Example from hooks.py:</h4>
            <pre><code>"Additional Salary": {
    "before_validate": "csf_tz.csftz_hooks.additional_salary.set_employee_base_salary_in_hours",
},</code></pre>
        </section>

        <section id="submission-hooks" class="page-break">
            <h2>4. Submission Hooks</h2>

            <h3>4.1 before_submit()</h3>
            <div class="hook-info">
                <strong>When:</strong> Called before document submission<br>
                <strong>Use Case:</strong> Final validations, mandatory field checks
            </div>

            <h4>Example from Container Reception:</h4>
            <pre><code>def before_submit(self):
    if not self.clerk:
        frappe.throw("Clerk is missing, Please select clerk to proceed..!")</code></pre>

            <h3>4.2 on_submit()</h3>
            <div class="hook-info">
                <strong>When:</strong> Called after successful document submission<br>
                <strong>Use Case:</strong> Create related documents, update statuses, trigger workflows
            </div>

            <h4>Example from Container Reception:</h4>
            <pre><code>def on_submit(self):
    self.create_mbl_container()
    self.create_hbl_container()
    self.update_container_storage_days()
    self.update_cmo_status("Received")</code></pre>
        </section>

        <section id="update-hooks" class="page-break">
            <h2>5. Update Hooks</h2>

            <h3>5.1 on_update()</h3>
            <div class="hook-info">
                <strong>When:</strong> Called after any update to the document<br>
                <strong>Use Case:</strong> Sync related data, cache invalidation
            </div>

            <h4>Example from Employee Advance:</h4>
            <pre><code>def on_update(self):
    self.publish_update()</code></pre>

            <h3>5.2 on_update_after_submit()</h3>
            <div class="hook-info">
                <strong>When:</strong> Called after updating a submitted document<br>
                <strong>Use Case:</strong> Handle changes to submitted documents
            </div>

            <h4>Example from Employee Separation:</h4>
            <pre><code>def on_update_after_submit(self):
    self.create_task_and_notify_user()</code></pre>
        </section>

        <section id="deletion-hooks" class="page-break">
            <h2>6. Deletion Hooks</h2>

            <h3>6.1 on_trash()</h3>
            <div class="hook-info">
                <strong>When:</strong> Called when document is being deleted<br>
                <strong>Use Case:</strong> Clean up related data, prevent deletion
            </div>

            <h3>6.2 before_cancel()</h3>
            <div class="hook-info">
                <strong>When:</strong> Called before document cancellation<br>
                <strong>Use Case:</strong> Validate cancellation, prepare cleanup
            </div>

            <h3>6.3 on_cancel()</h3>
            <div class="hook-info">
                <strong>When:</strong> Called after document cancellation<br>
                <strong>Use Case:</strong> Update related documents, reverse transactions
            </div>

            <h4>Example from Employee Advance:</h4>
            <pre><code>def on_cancel(self):
    self.ignore_linked_doctypes = ("GL Entry", "Payment Ledger Entry")
    self.check_linked_payment_entry()
    self.set_status(update=True)</code></pre>
        </section>

        <section id="hook-registration" class="page-break">
            <h2>8. Hook Registration</h2>

            <h3>8.1 In Controller Files</h3>
            <p>Hooks are defined as methods in the controller class:</p>
            <pre><code>class MyDoctype(Document):
    def validate(self):
        # Validation logic here
        pass
    
    def on_submit(self):
        # Submission logic here
        pass</code></pre>

            <h3>8.2 In hooks.py</h3>
            <p>Hooks can be registered globally:</p>
            <pre><code>doc_events = {
    "Sales Invoice": {
        "before_submit": [
            "csf_tz.custom_api.validate_grand_total",
            "csf_tz.authotp.api.sales_invoice.before_submit",
        ],
        "on_submit": [
            "csf_tz.custom_api.validate_net_rate",
            "csf_tz.custom_api.create_delivery_note",
        ],
    },
}</code></pre>
        </section>

        <section id="best-practices" class="page-break">
            <h2>9. Best Practices</h2>

            <h3>9.1 Error Handling</h3>
            <pre><code>def validate(self):
    try:
        self.validate_business_rules()
    except Exception as e:
        frappe.throw(f"Validation failed: {str(e)}")</code></pre>

            <h3>9.2 Performance Considerations</h3>
            <pre><code>def before_save(self):
    # Use db_set for simple field updates to avoid recursion
    if self.status_changed:
        frappe.db.set_value("Related Doc", self.related_doc, "status", self.status)</code></pre>

            <h3>9.3 Validation Messages</h3>
            <pre><code>def validate(self):
    if not self.required_field:
        frappe.throw(
            _("Required Field is mandatory"),
            title=_("Missing Required Field")
        )</code></pre>
        </section>

        <section id="execution-order" class="page-break">
            <h2>10. Hook Execution Order</h2>

            <div class="tip">
                <h4>Document Creation:</h4>
                <p><strong>before_insert()</strong> → <strong>validate()</strong> → <strong>before_save()</strong> → <strong>after_insert()</strong></p>

                <h4>Document Update:</h4>
                <p><strong>validate()</strong> → <strong>before_save()</strong> → <strong>on_update()</strong></p>

                <h4>Document Submission:</h4>
                <p><strong>before_submit()</strong> → <strong>on_submit()</strong> → <strong>after_submit()</strong></p>

                <h4>Document Cancellation:</h4>
                <p><strong>before_cancel()</strong> → <strong>on_cancel()</strong></p>

                <h4>Document Deletion:</h4>
                <p><strong>on_trash()</strong> → <strong>after_delete()</strong></p>
            </div>
        </section>

        <footer style="margin-top: 50px; padding-top: 20px; border-top: 1px solid #dee2e6; text-align: center; color: #6c757d;">
            <p><strong>End of Documentation</strong></p>
            <p><em>This documentation is based on analysis of the actual codebase and provides real-world examples of controller hook implementations.</em></p>
        </footer>
    </div>
</body>
</html>
