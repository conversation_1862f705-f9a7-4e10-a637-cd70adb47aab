import frappe
from frappe.utils import flt
from hrms.overrides.employee_payment_entry import get_payment_entry_for_employee

# from hrms.hr.doctype.expense_claim.expense_claim import get_expense_claim


def execute(doc, method):
    if doc.docstatus != 1:
        return

    if not doc.travel_request_ref:
        return

    try:
        # Run the entire operation with ignored permissions
        frappe.flags.ignore_permissions = True
        payment_entry = create_payment_entry(doc)
        doc.reload()

    except Exception as e:
        frappe.throw(f"Error during Employee Advance submission: {str(e)}")
    finally:
        # Reset the flag
        frappe.flags.ignore_permissions = False


def create_payment_entry(doc):
    try:
        # Store current user
        current_user = frappe.session.user

        # Temporarily switch to Administrator to bypass permissions
        frappe.set_user("Administrator")

        payment_entry = get_payment_entry_for_employee("Employee Advance", doc.name)

        if payment_entry:
            payment_entry.reference_no = doc.name
            payment_entry.reference_date = frappe.utils.nowdate()

            # Save the payment entry with ignore_permissions
            payment_entry.insert(ignore_permissions=True)

            # Optionally submit the payment entry as well
            # payment_entry.submit()

            frappe.msgprint(f"Payment Entry {payment_entry.name} created successfully")

            return payment_entry

    except Exception as e:
        frappe.throw(f"Error creating Payment Entry: {str(e)}")
    finally:
        # Restore original user
        frappe.set_user(current_user)
