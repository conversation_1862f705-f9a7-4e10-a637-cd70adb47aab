# Copyright (c) 2024, Frappe Technologies Pvt. Ltd. and contributors
# For license information, please see license.txt

import frappe
from frappe import _


def get_employee_advance_summary(employee, start_date=None, end_date=None):
	"""
	Get Employee Advance summary for an employee
	
	Args:
		employee (str): Employee ID
		start_date (str, optional): Start date for filtering advances
		end_date (str, optional): End date for filtering advances
	
	Returns:
		dict: Summary of employee advances
	"""
	filters = {
		"employee": employee,
		"docstatus": 1
	}
	
	if start_date and end_date:
		filters["posting_date"] = ["between", [start_date, end_date]]
	
	advances = frappe.get_all(
		"Employee Advance",
		filters=filters,
		fields=[
			"name", "posting_date", "purpose", "advance_amount", 
			"paid_amount", "claimed_amount", "return_amount", "status"
		],
		order_by="posting_date desc"
	)
	
	# Calculate totals
	total_advance_amount = sum(advance.get("advance_amount", 0) for advance in advances)
	total_paid_amount = sum(advance.get("paid_amount", 0) for advance in advances)
	total_claimed_amount = sum(advance.get("claimed_amount", 0) for advance in advances)
	total_return_amount = sum(advance.get("return_amount", 0) for advance in advances)
	
	pending_advances = [advance for advance in advances if advance.get("status") in ["Paid", "Unpaid"]]
	total_pending_amount = sum(
		advance.get("paid_amount", 0) - advance.get("claimed_amount", 0) - advance.get("return_amount", 0) 
		for advance in pending_advances
	)
	
	return {
		"advances": advances,
		"summary": {
			"total_advance_amount": total_advance_amount,
			"total_paid_amount": total_paid_amount,
			"total_claimed_amount": total_claimed_amount,
			"total_return_amount": total_return_amount,
			"total_pending_amount": total_pending_amount,
			"count": len(advances)
		}
	}


def get_advance_deductions_in_salary_slip(salary_slip_name):
	"""
	Get advance deductions included in a salary slip
	
	Args:
		salary_slip_name (str): Salary Slip name
	
	Returns:
		list: List of advance deductions
	"""
	salary_slip = frappe.get_doc("Salary Slip", salary_slip_name)
	advance_deductions = []
	
	for deduction in salary_slip.deductions:
		if deduction.additional_salary:
			ref_doctype, ref_docname = frappe.db.get_value(
				"Additional Salary", 
				deduction.additional_salary, 
				["ref_doctype", "ref_docname"]
			) or [None, None]
			
			if ref_doctype == "Employee Advance":
				advance_info = frappe.db.get_value(
					"Employee Advance",
					ref_docname,
					["purpose", "advance_amount", "paid_amount", "status"],
					as_dict=True
				)
				
				advance_deductions.append({
					"salary_component": deduction.salary_component,
					"amount": deduction.amount,
					"employee_advance": ref_docname,
					"advance_purpose": advance_info.get("purpose") if advance_info else "",
					"advance_amount": advance_info.get("advance_amount") if advance_info else 0,
					"advance_paid_amount": advance_info.get("paid_amount") if advance_info else 0,
					"advance_status": advance_info.get("status") if advance_info else ""
				})
	
	return advance_deductions


@frappe.whitelist()
def create_advance_deduction_additional_salary(employee_advance, salary_component, amount, payroll_date):
	"""
	Create Additional Salary for Employee Advance deduction
	
	Args:
		employee_advance (str): Employee Advance name
		salary_component (str): Salary Component for deduction
		amount (float): Deduction amount
		payroll_date (str): Payroll date
	
	Returns:
		dict: Created Additional Salary document
	"""
	advance_doc = frappe.get_doc("Employee Advance", employee_advance)
	
	# Validate if advance can be deducted
	remaining_amount = advance_doc.paid_amount - advance_doc.claimed_amount - advance_doc.return_amount
	if remaining_amount <= 0:
		frappe.throw(_("No amount remaining to deduct from Employee Advance {0}").format(employee_advance))
	
	if amount > remaining_amount:
		frappe.throw(
			_("Deduction amount {0} cannot be greater than remaining advance amount {1}").format(
				amount, remaining_amount
			)
		)
	
	# Create Additional Salary
	additional_salary = frappe.new_doc("Additional Salary")
	additional_salary.employee = advance_doc.employee
	additional_salary.salary_component = salary_component
	additional_salary.type = "Deduction"
	additional_salary.amount = amount
	additional_salary.payroll_date = payroll_date
	additional_salary.company = advance_doc.company
	additional_salary.currency = advance_doc.currency
	additional_salary.ref_doctype = "Employee Advance"
	additional_salary.ref_docname = employee_advance
	additional_salary.overwrite_salary_structure_amount = 0
	
	additional_salary.insert()
	additional_salary.submit()
	
	return additional_salary.as_dict()


@frappe.whitelist()
def get_employee_advance_balance(employee):
	"""
	Get total advance balance for an employee
	
	Args:
		employee (str): Employee ID
	
	Returns:
		dict: Advance balance details
	"""
	advances = frappe.get_all(
		"Employee Advance",
		filters={
			"employee": employee,
			"docstatus": 1,
			"status": ["in", ["Paid", "Unpaid", "Partly Claimed and Returned"]]
		},
		fields=["paid_amount", "claimed_amount", "return_amount"]
	)
	
	total_balance = sum(
		advance.get("paid_amount", 0) - advance.get("claimed_amount", 0) - advance.get("return_amount", 0)
		for advance in advances
	)
	
	return {
		"employee": employee,
		"total_advance_balance": total_balance,
		"advance_count": len(advances)
	}


def validate_advance_deduction_in_salary_slip(doc, method=None):
	"""
	Validate advance deductions in salary slip
	This can be used as a hook method
	"""
	if not doc.deductions:
		return
	
	total_advance_deductions = 0
	advance_deductions = get_advance_deductions_in_salary_slip(doc.name)
	
	for deduction in advance_deductions:
		total_advance_deductions += deduction.get("amount", 0)
	
	# Get employee's total advance balance
	balance_info = get_employee_advance_balance(doc.employee)
	total_balance = balance_info.get("total_advance_balance", 0)
	
	if total_advance_deductions > total_balance:
		frappe.msgprint(
			_("Warning: Total advance deductions ({0}) exceed available advance balance ({1})").format(
				total_advance_deductions, total_balance
			),
			alert=True
		)
