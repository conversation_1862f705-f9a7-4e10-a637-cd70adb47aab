{%- from "templates/print_formats/standard_macros.html" import add_header -%}

{{ add_header(0, 0, doc, letter_head, no_letterhead, 0) }}

<div class="page-break">
	<div class="row">
		<div class="col-xs-12">
			<h2 class="text-center">{{ _("Salary Slip") }}</h2>
		</div>
	</div>
	
	<div class="row">
		<div class="col-xs-6">
			<div class="row">
				<div class="col-xs-4"><label>{{ _("Employee") }}:</label></div>
				<div class="col-xs-8">{{ doc.employee }}</div>
			</div>
			<div class="row">
				<div class="col-xs-4"><label>{{ _("Employee Name") }}:</label></div>
				<div class="col-xs-8">{{ doc.employee_name }}</div>
			</div>
			<div class="row">
				<div class="col-xs-4"><label>{{ _("Department") }}:</label></div>
				<div class="col-xs-8">{{ doc.department or "" }}</div>
			</div>
			<div class="row">
				<div class="col-xs-4"><label>{{ _("Designation") }}:</label></div>
				<div class="col-xs-8">{{ doc.designation or "" }}</div>
			</div>
			<div class="row">
				<div class="col-xs-4"><label>{{ _("Branch") }}:</label></div>
				<div class="col-xs-8">{{ doc.branch or "" }}</div>
			</div>
		</div>
		<div class="col-xs-6">
			<div class="row">
				<div class="col-xs-4"><label>{{ _("Payroll Period") }}:</label></div>
				<div class="col-xs-8">{{ frappe.format_value(doc.start_date, {"fieldtype": "Date"}) }} {{ _("to") }} {{ frappe.format_value(doc.end_date, {"fieldtype": "Date"}) }}</div>
			</div>
			<div class="row">
				<div class="col-xs-4"><label>{{ _("Working Days") }}:</label></div>
				<div class="col-xs-8">{{ doc.total_working_days }}</div>
			</div>
			<div class="row">
				<div class="col-xs-4"><label>{{ _("Payment Days") }}:</label></div>
				<div class="col-xs-8">{{ doc.payment_days }}</div>
			</div>
			<div class="row">
				<div class="col-xs-4"><label>{{ _("Leave Without Pay") }}:</label></div>
				<div class="col-xs-8">{{ doc.leave_without_pay or 0 }}</div>
			</div>
			<div class="row">
				<div class="col-xs-4"><label>{{ _("Company") }}:</label></div>
				<div class="col-xs-8">{{ doc.company }}</div>
			</div>
		</div>
	</div>
	
	<hr>
	
	<!-- Employee Advance Section -->
	{% set employee_advances = frappe.get_all("Employee Advance", 
		filters={
			"employee": doc.employee, 
			"docstatus": 1,
			"status": ["in", ["Paid", "Unpaid", "Partly Claimed and Returned"]]
		}, 
		fields=["name", "posting_date", "purpose", "advance_amount", "paid_amount", "claimed_amount", "return_amount", "status"],
		order_by="posting_date desc",
		limit=5
	) %}
	
	{% if employee_advances %}
	<div class="row">
		<div class="col-xs-12">
			<h4>{{ _("Recent Employee Advances") }}</h4>
			<table class="table table-bordered">
				<thead>
					<tr>
						<th>{{ _("Advance ID") }}</th>
						<th>{{ _("Date") }}</th>
						<th>{{ _("Purpose") }}</th>
						<th>{{ _("Advance Amount") }}</th>
						<th>{{ _("Paid Amount") }}</th>
						<th>{{ _("Claimed Amount") }}</th>
						<th>{{ _("Return Amount") }}</th>
						<th>{{ _("Status") }}</th>
					</tr>
				</thead>
				<tbody>
					{% for advance in employee_advances %}
					<tr>
						<td>{{ advance.name }}</td>
						<td>{{ frappe.format_value(advance.posting_date, {"fieldtype": "Date"}) }}</td>
						<td>{{ advance.purpose }}</td>
						<td>{{ frappe.format_value(advance.advance_amount, {"fieldtype": "Currency"}) }}</td>
						<td>{{ frappe.format_value(advance.paid_amount, {"fieldtype": "Currency"}) }}</td>
						<td>{{ frappe.format_value(advance.claimed_amount, {"fieldtype": "Currency"}) }}</td>
						<td>{{ frappe.format_value(advance.return_amount, {"fieldtype": "Currency"}) }}</td>
						<td>{{ advance.status }}</td>
					</tr>
					{% endfor %}
				</tbody>
			</table>
		</div>
	</div>
	<hr>
	{% endif %}
	
	<!-- Earnings and Deductions Section -->
	<div class="row">
		<div class="col-xs-6">
			<h4>{{ _("Earnings") }}</h4>
			<table class="table table-bordered">
				<thead>
					<tr>
						<th>{{ _("Component") }}</th>
						<th class="text-right">{{ _("Amount") }}</th>
					</tr>
				</thead>
				<tbody>
					{% for earning in doc.earnings %}
					<tr>
						<td>{{ earning.salary_component }}</td>
						<td class="text-right">{{ frappe.format_value(earning.amount, {"fieldtype": "Currency"}) }}</td>
					</tr>
					{% endfor %}
				</tbody>
				<tfoot>
					<tr>
						<th>{{ _("Gross Pay") }}</th>
						<th class="text-right">{{ frappe.format_value(doc.gross_pay, {"fieldtype": "Currency"}) }}</th>
					</tr>
				</tfoot>
			</table>
		</div>
		
		<div class="col-xs-6">
			<h4>{{ _("Deductions") }}</h4>
			<table class="table table-bordered">
				<thead>
					<tr>
						<th>{{ _("Component") }}</th>
						<th class="text-right">{{ _("Amount") }}</th>
					</tr>
				</thead>
				<tbody>
					{% for deduction in doc.deductions %}
					<tr>
						<td>
							{{ deduction.salary_component }}
							<!-- Show if this deduction is related to Employee Advance -->
							{% if deduction.additional_salary %}
								{% set advance_ref = frappe.db.get_value("Additional Salary", deduction.additional_salary, ["ref_doctype", "ref_docname"]) %}
								{% if advance_ref and advance_ref[0] == "Employee Advance" %}
									<br><small class="text-muted">({{ _("Advance") }}: {{ advance_ref[1] }})</small>
								{% endif %}
							{% endif %}
						</td>
						<td class="text-right">{{ frappe.format_value(deduction.amount, {"fieldtype": "Currency"}) }}</td>
					</tr>
					{% endfor %}
				</tbody>
				<tfoot>
					<tr>
						<th>{{ _("Total Deductions") }}</th>
						<th class="text-right">{{ frappe.format_value(doc.total_deduction, {"fieldtype": "Currency"}) }}</th>
					</tr>
				</tfoot>
			</table>
		</div>
	</div>
	
	<hr>
	
	<!-- Net Pay Section -->
	<div class="row">
		<div class="col-xs-12">
			<table class="table table-bordered">
				<tr>
					<th class="text-right" style="width: 70%;">{{ _("Net Pay") }}</th>
					<th class="text-right" style="width: 30%;">{{ frappe.format_value(doc.net_pay, {"fieldtype": "Currency"}) }}</th>
				</tr>
				{% if doc.rounded_total != doc.net_pay %}
				<tr>
					<th class="text-right">{{ _("Rounded Total") }}</th>
					<th class="text-right">{{ frappe.format_value(doc.rounded_total, {"fieldtype": "Currency"}) }}</th>
				</tr>
				{% endif %}
			</table>
		</div>
	</div>
	
	{% if doc.total_in_words %}
	<div class="row">
		<div class="col-xs-12">
			<p><strong>{{ _("Amount in words") }}:</strong> {{ doc.total_in_words }}</p>
		</div>
	</div>
	{% endif %}
	
	<div class="row" style="margin-top: 30px;">
		<div class="col-xs-6">
			<p>{{ _("Employee Signature") }}</p>
			<br><br>
			<p>_________________________</p>
		</div>
		<div class="col-xs-6">
			<p>{{ _("Authorized Signatory") }}</p>
			<br><br>
			<p>_________________________</p>
		</div>
	</div>
</div>
