# Copyright (c) 2024, Frappe Technologies Pvt. Ltd. and contributors
# For license information, please see license.txt

import frappe
from frappe import _


def install_salary_slip_advance_print_format():
	"""Install the Salary Slip with Advance print format"""
	
	# Check if print format already exists
	if frappe.db.exists("Print Format", "Salary Slip with Advance"):
		print("Print Format 'Salary Slip with Advance' already exists")
		return
	
	try:
		# Create the print format
		print_format = frappe.new_doc("Print Format")
		print_format.name = "Salary Slip with Advance"
		print_format.doc_type = "Salary Slip"
		print_format.module = "Payroll"
		print_format.standard = "No"
		print_format.custom_format = 1
		print_format.print_format_type = "Jinja"
		print_format.disabled = 0
		
		# Read the HTML content
		import os
		html_path = os.path.join(
			frappe.get_app_path("hrms"),
			"payroll", "print_format", "salary_slip_with_advance", 
			"salary_slip_with_advance.html"
		)
		
		if os.path.exists(html_path):
			with open(html_path, 'r') as f:
				print_format.html = f.read()
		else:
			print(f"HTML file not found at {html_path}")
			return
		
		print_format.insert(ignore_permissions=True)
		print("Successfully installed 'Salary Slip with Advance' print format")
		
	except Exception as e:
		print(f"Error installing print format: {str(e)}")


def execute():
	"""Execute the installation"""
	install_salary_slip_advance_print_format()


if __name__ == "__main__":
	execute()
