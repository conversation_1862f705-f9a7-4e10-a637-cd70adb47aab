# Frappe Framework Controllers and Controller Hooks Documentation

**Version:** 1.0  
**Date:** June 30, 2025  
**Author:** Codebase Analysis  

---

## Table of Contents

1. [Controller Overview](#controller-overview)
2. [Document Lifecycle Hooks](#document-lifecycle-hooks)
3. [Validation Hooks](#validation-hooks)
4. [Submission Hooks](#submission-hooks)
5. [Update Hooks](#update-hooks)
6. [Deletion Hooks](#deletion-hooks)
7. [Custom Hooks](#custom-hooks)
8. [Hook Registration](#hook-registration)
9. [Best Practices](#best-practices)

---

## Controller Overview

Controllers in Frappe are Python classes that inherit from `Document` and contain business logic for doctypes. They provide hooks that are automatically called during the document lifecycle.

```python
from frappe.model.document import Document

class MyDoctype(Document):
    def validate(self):
        # Custom validation logic
        pass
```

**Key Points:**
- Controllers handle business logic for doctypes
- Hooks are automatically triggered during document operations
- Multiple hooks can be registered for the same event
- Hooks can be defined in controller files or registered globally in hooks.py

---

## Document Lifecycle Hooks

### 1. before_insert()

**When:** Called before a new document is inserted into the database  
**Use Case:** Set default values, generate auto-fields, prepare data

**Example from Container Reception:**
```python
def before_insert(self):
    self.posting_datetime = now_datetime()
```

**Example from Therapy Plan:**
```python
def before_insert(doc, method):
    total_sessions = 0
    total_sessions_cancelled = 0
    for entry in doc.therapy_plan_details:
        if entry.no_of_sessions:
            total_sessions += entry.no_of_sessions
        if entry.sessions_cancelled:
            total_sessions_cancelled += entry.sessions_cancelled
    
    doc.total_sessions = total_sessions
    doc.total_sessions_cancelled = total_sessions_cancelled
```

### 2. after_insert()

**When:** Called after a new document is successfully inserted  
**Use Case:** Create related documents, update linked records, send notifications

**Example from Container:**
```python
def after_insert(self):
    if self.container_no and self.container_reception:
        self.update_m_bl_based_container_details()
        self.update_hbl_based_container_details()
        self.validate_place_of_destination()
        self.posting_date = getdate(self.creation)
        self.db_update()
        self.db_update_all()
```

**Example from In Yard Container Booking:**
```python
def after_insert(self):
    frappe.db.set_value(
        "Container",
        self.container_id,
        "status",
        "At Booking"
    )
```

### 3. before_save()

**When:** Called before every save operation (insert and update)  
**Use Case:** Data transformation, calculations, setting computed fields

**Example from Container:**
```python
def before_save(self):
    self.validate_place_of_destination()
    self.update_container_reception()
    self.update_billed_days()
    self.update_billed_details()
    self.check_corridor_levy_eligibility()
    self.check_removal_charges_elibility()
```

**Example from Container Reception:**
```python
def before_save(self):
    if not self.company:
        self.company = frappe.defaults.get_user_default("Company")
    
    if not self.posting_date:
        self.posting_date = nowdate()
```

---

## Validation Hooks

### 4. validate()

**When:** Called during validation phase before save  
**Use Case:** Business rule validation, data integrity checks

**Example from Employee Advance:**
```python
def validate(self):
    validate_active_employee(self.employee)
    self.validate_exchange_rate()
    self.set_status()
    self.set_pending_amount()
```

**Example from Company Budget Control:**
```python
def validate(self):
    self.validate_required_fields()
    self.validate_fixed_target_logic()
    self.validate_unique_company()

def validate_required_fields(self):
    if self.target_calculation_method in ["Fixed Target", "Revenue-Based Target"] and not self.frequency:
        frappe.throw("Please select a <b>Frequency</b> for revenue calculations.")
```

### 5. before_validate()

**When:** Called before the validate() method  
**Use Case:** Prepare data for validation

**Example from hooks.py:**
```python
"Additional Salary": {
    "before_validate": "csf_tz.csftz_hooks.additional_salary.set_employee_base_salary_in_hours",
},
```

---

## Submission Hooks

### 6. before_submit()

**When:** Called before document submission  
**Use Case:** Final validations, mandatory field checks

**Example from Container Reception:**
```python
def before_submit(self):
    if not self.clerk:
        frappe.throw("Clerk is missing, Please select clerk to proceed..!")
```

**Example from Employee Advance:**
```python
def before_submit(self):
    if not self.get("advance_account"):
        default_advance_account = frappe.db.get_value(
            "Company", self.company, "default_employee_advance_account"
        )
        if default_advance_account:
            self.advance_account = default_advance_account
        else:
            frappe.throw(
                _(
                    'Advance Account is mandatory. Please set the Default Employee Advance Account in Company {0}.'
                ).format(self.company),
                title=_("Missing Advance Account"),
            )
```

### 7. on_submit()

**When:** Called after successful document submission  
**Use Case:** Create related documents, update statuses, trigger workflows

**Example from Container Reception:**
```python
def on_submit(self):
    self.create_mbl_container()
    self.create_hbl_container()
    self.update_container_storage_days()
    self.update_cmo_status("Received")
```

### 8. after_submit()

**When:** Called after document submission is complete  
**Use Case:** Post-submission processing, notifications

---

## Update Hooks

### 9. on_update()

**When:** Called after any update to the document  
**Use Case:** Sync related data, cache invalidation

**Example from Employee Advance:**
```python
def on_update(self):
    self.publish_update()
```

### 10. on_update_after_submit()

**When:** Called after updating a submitted document  
**Use Case:** Handle changes to submitted documents

**Example from Employee Separation:**
```python
def on_update_after_submit(self):
    self.create_task_and_notify_user()
```

---

## Deletion Hooks

### 11. on_trash()

**When:** Called when document is being deleted  
**Use Case:** Clean up related data, prevent deletion

**Example from Sales Order API:**
```python
def on_trash(doc, method):
    unlink_sales_order(doc)

def unlink_sales_order(doc):
    if not doc.m_bl_no:
        return
    
    service_orders = frappe.db.get_all("Service Order", filters={"sales_order": doc.name})
    if len(service_orders) > 0:
        for row in service_orders:
            frappe.db.set_value(
                "Service Order",
                row.name,
                "sales_order",
                "",
                update_modified=False
            )
```

### 12. before_cancel()

**When:** Called before document cancellation  
**Use Case:** Validate cancellation, prepare cleanup

**Example from Container Reception:**
```python
def before_cancel(self):
    self.cancel_linked_docs()
```

### 13. on_cancel()

**When:** Called after document cancellation  
**Use Case:** Update related documents, reverse transactions

**Example from Employee Advance:**
```python
def on_cancel(self):
    self.ignore_linked_doctypes = ("GL Entry", "Payment Ledger Entry")
    self.check_linked_payment_entry()
    self.set_status(update=True)
```

### 14. after_delete()

**When:** Called after document is permanently deleted  
**Use Case:** Final cleanup, cache invalidation

**Example from Employee Advance:**
```python
def after_delete(self):
    self.publish_update()
```

---

## Custom Hooks

### 15. onload()

**When:** Called when document is loaded in the UI  
**Use Case:** Set UI-specific data, load related information

**Example from Employee Advance:**
```python
def onload(self):
    self.get("__onload").make_payment_via_journal_entry = frappe.db.get_single_value(
        "Accounts Settings", "make_payment_via_journal_entry"
    )
```

### 16. before_rename()

**When:** Called before document is renamed  
**Use Case:** Validate rename operation, prepare data

---

## Hook Registration

### In Controller Files
Hooks are defined as methods in the controller class:

```python
class MyDoctype(Document):
    def validate(self):
        # Validation logic here
        pass
    
    def on_submit(self):
        # Submission logic here
        pass
```

### In hooks.py
Hooks can be registered globally:

```python
doc_events = {
    "Sales Invoice": {
        "before_submit": [
            "csf_tz.custom_api.validate_grand_total",
            "csf_tz.authotp.api.sales_invoice.before_submit",
        ],
        "on_submit": [
            "csf_tz.custom_api.validate_net_rate",
            "csf_tz.custom_api.create_delivery_note",
        ],
        "validate": [
            "csf_tz.custom_api.check_validate_delivery_note",
            "csf_tz.custom_api.validate_items_remaining_qty",
        ],
    },
    "Employee Advance": {
        "on_submit": "csf_tz.csftz_hooks.employee_advance_payment_and_expense.execute",
    },
}
```

### Universal Hooks
Apply hooks to all doctypes:

```python
doc_events = {
    "*": {
        "on_update": [
            "frappe.desk.notifications.clear_doctype_notifications",
            "frappe.workflow.doctype.workflow_action.workflow_action.process_workflow_actions",
        ],
    },
}
```

---

## Best Practices

### 1. Error Handling
```python
def validate(self):
    try:
        self.validate_business_rules()
    except Exception as e:
        frappe.throw(f"Validation failed: {str(e)}")
```

### 2. Performance Considerations
```python
def before_save(self):
    # Use db_set for simple field updates to avoid recursion
    if self.status_changed:
        frappe.db.set_value("Related Doc", self.related_doc, "status", self.status)
```

### 3. Conditional Logic
```python
def on_submit(self):
    if self.document_type == "Invoice":
        self.create_payment_entry()
    elif self.document_type == "Receipt":
        self.update_inventory()
```

### 4. Calling Parent Methods
```python
def validate(self):
    super().validate()  # Call parent class validation
    self.custom_validation()
```

### 5. Using Flags
```python
def on_update(self):
    if not self.flags.ignore_update_hooks:
        self.update_related_documents()
```

### 6. Database Operations
```python
def after_insert(self):
    # Use ignore_permissions for system operations
    related_doc = frappe.new_doc("Related DocType")
    related_doc.reference = self.name
    related_doc.insert(ignore_permissions=True)
```

### 7. Validation Messages
```python
def validate(self):
    if not self.required_field:
        frappe.throw(
            _("Required Field is mandatory"),
            title=_("Missing Required Field")
        )
```

---

## Hook Execution Order

1. **before_insert()** → **validate()** → **before_save()** → **after_insert()**
2. **validate()** → **before_save()** → **on_update()**
3. **before_submit()** → **on_submit()** → **after_submit()**
4. **before_cancel()** → **on_cancel()**
5. **on_trash()** → **after_delete()**

---

## Common Use Cases

### Data Validation
- Use `validate()` for business rule checks
- Use `before_validate()` for data preparation
- Use `before_submit()` for final validation

### Related Document Creation
- Use `after_insert()` for creating dependent documents
- Use `on_submit()` for workflow-triggered document creation

### Status Updates
- Use `on_submit()` and `on_cancel()` for status changes
- Use `on_update()` for real-time status synchronization

### Cleanup Operations
- Use `on_trash()` for cleaning up related data
- Use `after_delete()` for final cleanup tasks

---

**End of Documentation**

*This documentation is based on analysis of the actual codebase and provides real-world examples of controller hook implementations.*
